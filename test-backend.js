// Simple script to test backend connectivity
const fetch = require('node-fetch');

const BACKEND_URL = 'http://localhost:2340';

async function testBackend() {
  console.log('🔍 Testing backend connectivity...');
  
  try {
    // Test basic connectivity
    console.log('1. Testing basic connectivity...');
    const healthRes = await fetch(`${BACKEND_URL}/health`);
    console.log('Health check status:', healthRes.status);
    
    if (healthRes.ok) {
      const healthData = await healthRes.json();
      console.log('✅ Health check response:', healthData);
    } else {
      console.log('❌ Health check failed');
    }

    // Test root endpoint
    console.log('\n2. Testing root endpoint...');
    const rootRes = await fetch(`${BACKEND_URL}/`);
    console.log('Root endpoint status:', rootRes.status);
    
    if (rootRes.ok) {
      const rootData = await rootRes.json();
      console.log('✅ Root response:', rootData);
    } else {
      console.log('❌ Root endpoint failed');
    }

    // Test auth endpoint
    console.log('\n3. Testing auth endpoint...');
    const authRes = await fetch(`${BACKEND_URL}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'test123'
      })
    });
    
    console.log('Auth endpoint status:', authRes.status);
    const authData = await authRes.json();
    console.log('Auth response:', authData);

  } catch (error) {
    console.error('💥 Connection failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend server is running');
    console.log('2. Check if it\'s running on port 2340');
    console.log('3. Verify MongoDB connection');
    console.log('4. Check environment variables');
  }
}

testBackend();
