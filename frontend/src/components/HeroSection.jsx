import { Link } from "react-router-dom";
import { useState, useEffect } from "react";
import video from "../assets/backgroundV.mp4";

export default function HeroSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    setIsVisible(true);

    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="relative flex justify-center items-center h-[85vh] w-full p-2 overflow-hidden">
      {/* Animated gradient background */}
      <div
        className="absolute inset-0 opacity-30 transition-all duration-1000 ease-out"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(34, 197, 94, 0.3) 0%, rgba(168, 85, 247, 0.2) 50%, rgba(59, 130, 246, 0.1) 100%)`
        }}
      ></div>

      {/* Main content container */}
      <div className={`bg-[#8181811f] w-[90%] py-20 px-6 sm:px-10 md:px-16 lg:px-24 xl:px-32 h-[90%] rounded-[20px] relative z-20 backdrop-blur-sm border border-white/10 shadow-2xl transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        {/* Background video for the container */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute z-[-1] w-full h-full object-cover rounded-[20px] opacity-[0.4] top-0 left-0 transition-opacity duration-500 hover:opacity-[0.6]"
        >
          <source src={video} type="video/mp4" />
        </video>

        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden rounded-[20px]">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 bg-lime-400/30 rounded-full animate-pulse`}
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + (i % 3) * 20}%`,
                animationDelay: `${i * 0.5}s`,
                animationDuration: `${3 + i * 0.5}s`
              }}
            ></div>
          ))}
        </div>

        {/* Desktop: Left-Right Layout, Mobile: Stacked */}
        <div className="flex flex-col lg:flex-row items-center justify-between h-full gap-8 lg:gap-12">

          {/* Left Side: Text Content */}
          <div className={`flex-1 text-center lg:text-left max-w-2xl transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>
            {/* Headline */}
            <h1 className="text-white text-2xl text-[28px] sm:text-3xl lg:text-4xl font-extrabold leading-tight FontLato mb-4 relative">
              <span className="text-lime-300 bg-gradient-to-r from-lime-300 via-green-400 to-emerald-400 bg-clip-text text-transparent animate-pulse">
                Stop chasing influencers. Get verified UGC on autopilot.
              </span>
              {/* Glowing underline effect */}
              <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-lime-400/0 via-lime-400/60 to-lime-400/0 rounded-full animate-pulse"></div>
            </h1>

            {/* Subheadline */}
            <p className={`text-gray-200 text-base sm:text-lg lg:text-xl max-w-3xl mx-auto lg:mx-0 FontNoto mb-4 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
              No matter what you sell — products or services — Matchably connects you with real creators who deliver authentic content. No DMs. No ghosting. Just real results.
            </p>

            {/* Tagline */}
            <p className={`text-lime-300 text-sm sm:text-base font-semibold FontNoto mb-8 transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
              <span className="relative">
                UGC = Marketing Asset, Not Just a Post.
                <div className="absolute inset-0 bg-lime-400/20 blur-lg rounded-lg -z-10 animate-pulse"></div>
              </span>
            </p>

            {/* CTA Buttons */}
            <div className={`flex flex-col sm:flex-row gap-4 justify-center lg:justify-start transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <Link
                to="/brand-signup"
                className="group relative text-white px-6 md:px-8 py-3 md:py-3.5 rounded-[25px] text-sm md:text-lg font-semibold FontNoto
                  bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500
                  border border-white/30 hover:border-white/60
                  shadow-[0_0_20px_rgba(255,0,255,0.3)] hover:shadow-[0_0_30px_rgba(0,255,255,0.5)]
                  transition-all duration-500 transform hover:scale-105 hover:-translate-y-1
                  min-w-[180px] text-center overflow-hidden"
              >
                <span className="relative z-10">I'm a Brand</span>
                <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12"></div>
              </Link>
              <Link
                to="/campaigns"
                className="group relative text-white px-6 md:px-8 py-3 md:py-3.5 rounded-[25px] text-sm md:text-lg font-semibold FontNoto
                  bg-gradient-to-r from-green-500 via-teal-500 to-cyan-500
                  border border-white/30 hover:border-white/60
                  shadow-[0_0_20px_rgba(0,255,255,0.3)] hover:shadow-[0_0_30px_rgba(255,0,255,0.5)]
                  transition-all duration-500 transform hover:scale-105 hover:-translate-y-1
                  min-w-[180px] text-center overflow-hidden"
              >
                <span className="relative z-10">I'm a Creator</span>
                <div className="absolute inset-0 bg-gradient-to-r from-green-600 via-teal-600 to-cyan-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12"></div>
              </Link>
            </div>
          </div>

          {/* Right Side: UGC Video */}
          <div className={`flex-shrink-0 w-full max-w-[300px] lg:max-w-[350px] transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>
            <div className="relative aspect-[9/16] lg:aspect-[4/5] rounded-2xl overflow-hidden shadow-2xl group">
              {/* Glowing border effect */}
              <div className="absolute -inset-1 bg-gradient-to-r from-lime-400 via-green-500 to-emerald-500 rounded-2xl opacity-30 group-hover:opacity-60 transition-opacity duration-500 blur-sm"></div>

              <div className="relative bg-black rounded-2xl overflow-hidden">
                <video
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                >
                  <source src={video} type="video/mp4" />
                </video>

                {/* Video overlay with play icon */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                      <p className="text-white text-xs font-semibold">UGC Content Preview</p>
                    </div>
                  </div>
                </div>

                {/* Floating play button */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30 hover:scale-110 transition-transform duration-300">
                    <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced pricing teaser banner at bottom */}
        <div className={`absolute bottom-4 left-1/2 transform -translate-x-1/2 transition-all duration-1000 delay-1200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
          <div className="group relative bg-gradient-to-r from-lime-500/20 to-green-500/20 backdrop-blur-sm border border-lime-400/30 text-white px-6 py-3 rounded-full text-sm font-semibold FontNoto hover:border-lime-400/60 transition-all duration-300 cursor-pointer">
            <span className="relative z-10 flex items-center gap-2">
              <span className="w-2 h-2 bg-lime-400 rounded-full animate-pulse"></span>
              Start Free Today
              <span className="text-lime-400">→</span>
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-lime-400/10 to-green-400/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
