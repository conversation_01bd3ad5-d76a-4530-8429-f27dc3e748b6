import React, { useState, useEffect } from 'react';

const BrandLogoCarousel = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('brand-logos');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  // Real brand logos from trusted companies
  const brands = [
    {
      name: 'Sensilab',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/5a6cc655-e417-4905-bbdc-47d724eb87b9.webp',
      category: 'Health & Wellness'
    },
    {
      name: 'Mini',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/98803f38-fed7-41db-ae64-c74a9961a450.png',
      category: 'Automotive'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/a7df3de9-790d-446c-ac74-337bf0f29d8c.webp',
      category: 'Pet Care'
    },
    {
      name: 'MyVariations',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/91966228-c8cf-41dc-bdc0-49ea09e7b25a.webp',
      category: 'Supplements'
    },
    {
      name: 'Little Fawn Box',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/b47915e3-fab2-4922-9e5e-111870b9411c.webp',
      category: 'Subscription Box'
    },
    {
      name: 'Rebuilt',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/0817f4e0-b54f-46f6-90be-a311230dd117.webp',
      category: 'Fitness'
    },
    {
      name: 'North Carry',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/64e5661a-c476-4d06-9ccf-f12901c871e3.webp',
      category: 'Accessories'
    },
    {
      name: 'Simpled Services',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/5c825433-8bed-42cc-a8d7-0dfc2040281e.webp',
      category: 'Digital Services'
    },
    {
      name: 'Dermz Laboratories',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/6439f30e-2933-4f28-9eb3-9963188791bc.webp',
      category: 'Skincare'
    },
    {
      name: 'Saltworks',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/f712a23f-56fc-4695-82c4-b0065d459aa5.webp',
      category: 'Home & Kitchen'
    },
    {
      name: 'MATCHBOX Jewellery',
      logo: 'https://influeestorage.b-cdn.net/company-photos-influee/c7a69f66-4e57-4dca-a9b4-82d6a58f0478.webp',
      category: 'Jewelry'
    },
    // Additional brands for variety
    {
      name: 'Kindred Fires',
      logo: 'https://via.placeholder.com/120x60/1a1a1a/ffffff?text=Kindred+Fires',
      category: 'Home & Garden'
    },
    {
      name: 'Polarwise',
      logo: 'https://via.placeholder.com/120x60/1a1a1a/ffffff?text=Polarwise',
      category: 'Personal Care'
    },
    {
      name: 'Tech Trove',
      logo: 'https://via.placeholder.com/120x60/1a1a1a/ffffff?text=Tech+Trove',
      category: 'Technology'
    },
    {
      name: 'BeautyBrand',
      logo: 'https://via.placeholder.com/120x60/1a1a1a/ffffff?text=BeautyBrand',
      category: 'Beauty'
    }
  ];

  // Duplicate brands for seamless infinite scroll
  const duplicatedBrands = [...brands, ...brands];

  return (
    <section id="brand-logos" className="w-full bg-[var(--background)] py-16 px-4 overflow-hidden relative">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/2 left-1/4 w-64 h-64 bg-lime-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 right-1/4 w-96 h-96 bg-green-400/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Title */}
        <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h2 className="text-[20px] md:text-[28px] text-lime-100 FontNoto leading-tight mb-4 relative">
            <span className="bg-gradient-to-r from-lime-300 via-green-400 to-emerald-400 bg-clip-text text-transparent">
              Trusted by brands of all types — from household names to emerging startups
            </span>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-lime-400/0 via-lime-400/60 to-lime-400/0 rounded-full"></div>
          </h2>
          <p className="text-gray-400 text-base FontNoto max-w-2xl mx-auto">
            Join over 1,500+ brands who trust us to deliver authentic UGC content
          </p>
        </div>

        {/* Logo Carousel Container */}
        <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
          {/* Enhanced gradient overlays */}
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-[var(--background)] via-[var(--background)]/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-[var(--background)] via-[var(--background)]/80 to-transparent z-10"></div>

          {/* Scrolling container */}
          <div className="brand-logo-carousel-wrapper overflow-hidden">
            <div className="logo-carousel-track flex items-center gap-16 brand-logo-animate-scroll">
              {duplicatedBrands.map((brand, index) => (
                <div
                  key={`${brand.name}-${index}`}
                  className="flex-shrink-0 flex flex-col items-center group cursor-pointer"
                >
                  {/* Logo container with enhanced styling */}
                  <div className="relative">
                    {/* Glowing border effect */}
                    <div className="absolute -inset-1 bg-gradient-to-r from-lime-400/20 via-green-500/20 to-emerald-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                    <div className="relative w-[140px] h-[70px] bg-gradient-to-br from-white to-gray-50 rounded-xl flex items-center justify-center p-4 shadow-lg group-hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group-hover:border-lime-400/30 group-hover:scale-105">
                      <img
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                        className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-500 group-hover:scale-110"
                        onError={(e) => {
                          e.target.src = `https://via.placeholder.com/140x70/1a1a1a/ffffff?text=${encodeURIComponent(brand.name)}`;
                        }}
                      />

                      {/* Success indicator */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  </div>

                  {/* Brand info */}
                  <div className="mt-4 text-center">
                    <p className="text-gray-300 text-sm FontNoto font-semibold group-hover:text-lime-400 transition-colors duration-300">
                      {brand.name}
                    </p>
                    <p className="text-gray-500 text-xs FontNoto mt-1 group-hover:text-gray-400 transition-colors duration-300">
                      {brand.category}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Stats section */}
        <div className={`mt-16 text-center transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="group">
              <div className="text-2xl md:text-3xl font-bold text-lime-400 FontNoto mb-2 group-hover:scale-110 transition-transform duration-300">
                1,500+
              </div>
              <p className="text-gray-400 text-sm FontNoto">
                Trusted Brands
              </p>
            </div>
            <div className="group">
              <div className="text-2xl md:text-3xl font-bold text-green-400 FontNoto mb-2 group-hover:scale-110 transition-transform duration-300">
                50K+
              </div>
              <p className="text-gray-400 text-sm FontNoto">
                UGC Videos Created
              </p>
            </div>
            <div className="group">
              <div className="text-2xl md:text-3xl font-bold text-emerald-400 FontNoto mb-2 group-hover:scale-110 transition-transform duration-300">
                95%
              </div>
              <p className="text-gray-400 text-sm FontNoto">
                Client Satisfaction
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced CSS for animations */}
      <style>{`
        @keyframes brandLogoScroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        @keyframes brandLogoFloat {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-5px);
          }
        }

        .brand-logo-animate-scroll {
          animation: brandLogoScroll 45s linear infinite;
        }

        .brand-logo-animate-scroll:hover {
          animation-play-state: paused;
        }

        .brand-logo-animate-scroll .group:hover {
          animation: brandLogoFloat 2s ease-in-out infinite;
        }

        .brand-logo-carousel-wrapper {
          mask-image: linear-gradient(
            to right,
            transparent 0%,
            black 8%,
            black 92%,
            transparent 100%
          );
          -webkit-mask-image: linear-gradient(
            to right,
            transparent 0%,
            black 8%,
            black 92%,
            transparent 100%
          );
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .brand-logo-animate-scroll {
            animation-duration: 35s;
          }
        }

        /* Performance optimization */
        .brand-logo-animate-scroll {
          will-change: transform;
        }
      `}</style>
    </section>
  );
};

export default BrandLogoCarousel;
