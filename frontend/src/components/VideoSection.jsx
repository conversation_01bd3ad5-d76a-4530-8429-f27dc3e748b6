import React, { useState, useEffect } from "react";
import video1 from "../assets/videof.mp4";
import video2 from "../assets/videos.mp4";
import video3 from "../assets/videoi.mp4";
import video4 from "../assets/Video.mp4";

const videoData = [
  { src: video1, title: "Beauty & Lifestyle", category: "Beauty" },
  { src: video2, title: "Tech & Innovation", category: "Technology" },
  { src: video3, title: "Fashion & Style", category: "Fashion" },
  { src: video4, title: "Health & Wellness", category: "Health" }
];

export default function VideoSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredVideo, setHoveredVideo] = useState(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('video-section');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  return (
    <section id="video-section" className="w-full py-14 px-4 sm:px-8 md:px-14 lg:px-20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/3 w-64 h-64 bg-lime-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-green-400/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10">
        {/* Section intro */}
        <div className={`text-center mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h3 className="text-xl md:text-2xl text-lime-100 FontNoto mb-4">
            <span className="bg-gradient-to-r from-lime-300 via-green-400 to-emerald-400 bg-clip-text text-transparent">
              See Our Work in Action
            </span>
          </h3>
          <p className="text-gray-400 FontNoto max-w-2xl mx-auto">
            Real UGC content created by our community of authentic creators
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {videoData.map((video, i) => (
            <div
              key={i}
              className={`group relative transition-all duration-700 delay-${i * 150} ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
              onMouseEnter={() => setHoveredVideo(i)}
              onMouseLeave={() => setHoveredVideo(null)}
            >
              {/* Glowing border effect */}
              <div className="absolute -inset-1 bg-gradient-to-r from-lime-400/30 via-green-500/30 to-emerald-500/30 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

              <div className="relative bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-sm p-4 rounded-2xl shadow-lg border border-white/10 group-hover:border-lime-400/30 transition-all duration-500 transform group-hover:scale-105 group-hover:shadow-2xl">

                {/* Category badge */}
                <div className="absolute top-6 left-6 z-20">
                  <div className="bg-lime-400/20 backdrop-blur-sm border border-lime-400/30 text-lime-400 px-2 py-1 rounded-full text-xs font-semibold">
                    {video.category}
                  </div>
                </div>

                <div className="relative overflow-hidden rounded-xl border border-white/20 group-hover:border-lime-400/40 transition-colors duration-500">
                  <video
                    src={video.src}
                    muted
                    loop
                    playsInline
                    autoPlay
                    className="w-full h-[48vh] object-cover bg-black rounded-xl transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Video overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                        <p className="text-white text-sm font-semibold FontNoto">
                          {video.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="w-2 h-2 bg-lime-400 rounded-full animate-pulse"></span>
                          <span className="text-lime-400 text-xs">UGC Content</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Play button overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                    <div className="relative">
                      <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20 hover:scale-110 transition-transform duration-300">
                        <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                      </div>
                      {/* Ripple effect */}
                      <div className="absolute inset-0 rounded-full border-2 border-white/30 animate-ping"></div>
                    </div>
                  </div>

                  {/* Floating particles effect */}
                  {hoveredVideo === i && (
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      {[...Array(4)].map((_, particleIndex) => (
                        <div
                          key={particleIndex}
                          className="absolute w-1 h-1 bg-lime-400/60 rounded-full animate-ping"
                          style={{
                            left: `${20 + particleIndex * 20}%`,
                            top: `${30 + (particleIndex % 2) * 40}%`,
                            animationDelay: `${particleIndex * 0.3}s`,
                            animationDuration: `${2 + particleIndex * 0.5}s`
                          }}
                        ></div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
