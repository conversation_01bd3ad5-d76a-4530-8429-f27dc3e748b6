/* Updated BrandPricingPage with UI/UX Enhancements */

import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { IoIosArrowForward } from "react-icons/io";

const plans = [
  {
    name: 'Single',
    price: '$450',
    campaigns: 1,
    creators: 10,
    perCreator: '$45',
  },
  {
    name: 'Starter',
    price: '$1,350',
    campaigns: 3,
    creators: 30,
    perCreator: '$45',
  },
  {
    name: 'Standard',
    price: '$2,150',
    campaigns: 5,
    creators: 50,
    perCreator: '$43',
    badge: 'Most Popular',
  },
  {
    name: 'Pro',
    price: '$2,800',
    campaigns: 7,
    creators: 70,
    perCreator: '$40',
    badge: 'Best Value',
  },
];

const Badge = ({ type }) => {
  const bgColor = type === 'Most Popular' ? 'bg-pink-500' : 'bg-blue-500';

  return (
    <div
      className={`absolute right-0 top-0 px-3 py-[6px] text-xs font-semibold text-white rounded-bl-lg ${bgColor} z-10`}
    >
      {type}
    </div>
  );
};



const BrandPricingPage = () => {
  const navigate = useNavigate();

  return (
    <div className="bg-black text-white px-4 md:px-12 lg:px-24 pt-12 pb-8 min-h-screen">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold leading-snug mb-2">
          Choose Your Campaign Plan
        </h1>
        <p className="text-gray-300 text-lg">Flexible plans for growing your brand with creators</p>
      </div>

      <div className="border-t border-gray-800 mt-16"></div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10 mt-12">
  {plans.map((plan, index) => (
    <div
      key={index}
      className="relative bg-[#111] p-10 rounded-2xl shadow-lg border border-gray-700 hover:border-green-500 hover:scale-105 transition-all duration-200 flex flex-col justify-between min-h-[420px]"
    >
      {plan.badge && <Badge type={plan.badge} />}
      <div>
        <h3 className="text-xl font-bold mb-2 text-white">{plan.name}</h3>
        <p className="text-3xl font-bold mb-8 text-green-400">{plan.price}</p>
        <ul className="space-y-3 text-base font-semibold text-gray-200">
          <li className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7h18M3 12h18M3 17h18" />
            </svg>
            {plan.campaigns} Campaign{plan.campaigns > 1 ? 's' : ''}
          </li>
          <li className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a4 4 0 00-4-4h-4a4 4 0 00-4 4v2h5" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14a4 4 0 100-8 4 4 0 000 8z" />
            </svg>
            Max {plan.creators} Creators
          </li>
          <li className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.5 0-2.7 1.2-2.7 2.7S10.5 13.4 12 13.4c1.5 0 2.7-1.2 2.7-2.7S13.5 8 12 8zm0 10v-2m0-4V6" />
            </svg>
            {plan.perCreator} per Creator
          </li>
        </ul>
      </div>
      <button
        onClick={() => navigate(`/brand/pricing`)}
        className="mt-8 bg-green-500 text-black py-2 rounded-xl hover:bg-green-400 font-semibold"
      >
        Select Plan
      </button>
    </div>
  ))}
      </div>

      <div className="border-t border-gray-800 mt-20"></div>

      <div className="mt-20">
  <h2 className="text-2xl font-bold text-white mb-8">➕ Add-on Options</h2>
  <div className="grid sm:grid-cols-2 gap-x-10 gap-y-6">
    {[
      {
        title: '① Extra Creator Add-on',
        highlight: '+10 creators',
        price: '$499',
        noteColor: 'green',
        notes: [
          '❗ Only for Starter plans and above',
          '❗ Max 20 creators per campaign',
        ],
        query: 'extra-creators',
      },
      {
        title: '② Extra Campaign Add-on',
        highlight: '+1 campaign',
        price: '$399',
        noteColor: 'green',
        notes: [
          '❗ Only after 80% of the original plan is used',
          '❗ Max 2 extra campaigns per plan',
        ],
        query: 'extra-campaign',
      },
    ].map((addon, idx) => (
      <div
        key={idx}
        className="bg-[#111] p-8 rounded-2xl border border-gray-700 flex flex-col justify-between min-h-[280px]"
      >
        <div>
          <h3 className="text-white font-semibold text-lg mb-3">{addon.title}</h3>
          <p className="text-base">
            <span className="text-green-400 font-semibold">{addon.highlight}</span> for {addon.price}
          </p>
          <ul className="text-sm font-medium mt-4 space-y-2">
            {addon.notes.map((note, i) => (
              <li key={i} className="text-red-400">{note}</li>
            ))}
          </ul>
        </div>
        <div className="mt-6 flex justify-center">
          <button
            onClick={() => navigate(`/checkout?addon=${addon.query}`)}
            className="bg-green-600 hover:bg-green-700 text-white py-2 px-5 rounded font-semibold"
          >
            Add to Checkout
          </button>
        </div>
      </div>
    ))}
  </div>
</div>

      <div className="border-t border-gray-800 mt-20"></div>

  <div className="mt-20">
  <h2 className="text-2xl font-bold text-white text-center mb-12">FAQs</h2>
  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
    {/* Left Column */}
    <div className="space-y-5">
      <h3 className="text-lg font-semibold text-white mb-2"> Payment & Plan Usage</h3>
      {[
        {
          q: 'When do I need to pay?',
          a: 'All plans are paid upfront at checkout.',
        },
        {
          q: 'Can I upgrade my plan later?',
          a: 'Yes — you can upgrade within 7 days by just paying the difference.',
        },
        {
          q: 'If I upgrade, do I get new creators?',
          a: 'Yes — your creator count resets, but creators already used in your previous plan cannot be reused.',
        },
        {
          q: 'Can I cancel or get a refund?',
          a: 'No — all purchases and add-ons are non-refundable.',
        },
      ].map((item, idx) => (
        <details key={idx} className="bg-[#111] p-5 rounded-xl border border-gray-700">
          <summary className="cursor-pointer text-white text-base font-medium">{item.q}</summary>
          <p className="mt-2 text-gray-300 text-sm leading-relaxed">{item.a}</p>
        </details>
      ))}
    </div>

    {/* Right Column */}
    <div className="space-y-5">
      <h3 className="text-lg font-semibold text-white mb-2"> Campaign & Creators</h3>
      {[
        {
          q: 'How many creators can I include in one campaign?',
          a: 'Up to 10 by default. With an add-on (Starter+), max 20 per campaign.',
        },
        {
          q: 'Can I use the same creator in multiple campaigns?',
          a: 'No — each creator can only be used once per plan.',
        },
        {
          q: 'What happens if a creator doesn’t upload content?',
          a: 'If a creator fails to upload their content without prior notice, they may be blocked from participating in future campaigns. Please report any missing content to <NAME_EMAIL>.',
        },
        {
          q: 'How many creators do I see and select per campaign?',
          a: `It depends on the option you choose:\n\n•  10 Creators Option\nYou see 20 applicants.\nYou can select up to 10 creators.\n→ Best for quick testing and fast feedback.\n\n•  15 Creators Option\nYou see 25 applicants.\nYou can select up to 15 creators.\n→ A balance between volume and content quality.\n\n• 20 Creators Option\nYou see 30 applicants.\nYou can select up to 20 creators.\n→ Maximizes reach and product visibility.`,
        },
      ].map((item, idx) => (
        <details key={idx} className="bg-[#111] p-5 rounded-xl border border-gray-700 whitespace-pre-line">
          <summary className="cursor-pointer text-white text-base font-medium">{item.q}</summary>
          <p className="mt-2 text-gray-300 text-sm leading-relaxed">{item.a}</p>
        </details>
      ))}
    </div>
  </div>
</div>


      <div className="border-t border-gray-800 mt-20"></div>

      <div className="text-center py-14">
        <h2 className="text-2xl font-bold mb-4 text-white">Ready to launch your campaign?</h2>
        <div className="flex justify-center gap-4 flex-wrap">
          <Link
            to="/brand/campaign/create"
            className="px-6 py-3 bg-green-500 text-black rounded-xl hover:bg-green-400 font-semibold text-lg"
          >
            Launch Campaign
          </Link>
          <button
            onClick={() => window.location.href = 'mailto:<EMAIL>'}
            className="px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 font-semibold text-lg"
          >
            Contact Sales
          </button>
        </div>
      </div>
    </div>
  );
};

export default BrandPricingPage;
