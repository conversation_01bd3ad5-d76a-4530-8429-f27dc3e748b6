import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [
    react(),
    tailwindcss()
  ],
  base: '/', // ✅ Important for routing
  build: {
    outDir: 'dist', // ✅ Required for correct publish directory
  },
  server: {
    port: 3000,
  },
  optimizeDeps: {
    include: ['zustand'],
  },
})
