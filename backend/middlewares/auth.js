const dotenv = require("dotenv");
const bcrypt = require("bcrypt");
const { User } = require("../database");
const axios = require("axios");
const {
  sendWelcomeEmail,
  sendVerificationLink,
} = require("../functions/sendEmail");
const { generateToken, verifyToken } = require("../utils/jwtUtils");

dotenv.config();

// ✅ Middleware to protect user routes
async function VerifyToken(req, res, next) {
	const token = req.headers.authorization;
  
	try {
	  const decoded = await verifyToken(token);
	  const user = await User.findOne({ email: decoded.email });
  
	  if (!user) {
		return res.json({ status: "failed", message: "User not found!" });
	  }
  
	  req.user = user; // ✅ FIXED: attach full user to request
	  next();
	} catch {
	  res.json({ status: "failed", message: "Invalid or expired token" });
	}
  }
  

// ✅ Email/password login
async function signin(req, res) {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) return res.json({ status: "failed", message: "User not found" });
    if (user.blocked) return res.json({ status: "failed", message: "User is blocked" });
    if (user.isGoogleUser) return res.json({ status: "failed", message: "Please login using Google" });
    if (!user.isVerified) return res.json({ status: "failed", message: "Please verify your email first" });

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) return res.json({ status: "failed", message: "Incorrect password" });

    const token = generateToken({ email });
    res.json({ status: "success", message: "Login successful", token });
  } catch {
    res.json({ status: "failed", message: "Something went wrong" });
  }
}

// ✅ Register new user
async function signup(req, res) {
  const { name, email, password, recaptchaToken } = req.body;

  // Step 1: Input Validation
  if (!name || !email || !password || !recaptchaToken) {
    return res.json({
      status: "failed",
      message: "Please provide name, email, password, and reCAPTCHA token",
    });
  }

  try {
    // Step 2: Verify reCAPTCHA
    const RECAPTCHA_SECRET = process.env.RECAPTCHA_SECRET_KEY;
    const captchaRes = await axios.post(
      `https://www.google.com/recaptcha/api/siteverify`,
      null,
      {
        params: {
          secret: RECAPTCHA_SECRET,
          response: recaptchaToken,
        },
      }
    );

    // Optional: log score for debugging
    // console.log("reCAPTCHA score:", captchaRes.data.score);

    if (!captchaRes.data.success || (captchaRes.data.score && captchaRes.data.score < 0.7)) {
      return res.json({
        status: "failed",
        message: "reCAPTCHA verification failed. Are you a bot?",
      });
    }

    // Step 3: Check for existing user
    const existing = await User.findOne({ email });
    if (existing) {
      return res.json({
        status: "failed",
        message: "User already registered",
      });
    }

    // Step 4: Create new user with hashed password
    const hash = await bcrypt.hash(password, 10);

    const newUser = new User({
      name,
      email,
      password: hash,
      isGoogleUser: false,
      isVerified: false,
    });

    await newUser.save();

    // Step 5: Send email verification link
    const token = generateToken({ email });
    await sendVerificationLink(email, token);

    res.json({
      status: "success",
      message: "Signup successful. Verification link sent to your email.",
    });
  } catch (error) {
    console.error("Signup error:", error.message);
    res.json({
      status: "failed",
      message: "Something went wrong during signup",
    });
  }
}


// ✅ Google login
async function googleAuth(req, res) {
  const { email, name } = req.body;

  try {
    if (!email || !name) return res.json({ status: "failed", message: "Missing email or name" });

    let user = await User.findOne({ email });

    if (!user) {
      const hashedDummyPassword = await bcrypt.hash("googleuser", 10);
      user = new User({
        name,
        email,
        isGoogleUser: true,
        password: hashedDummyPassword,
        isVerified: true, // Google users are considered verified
      });
      await user.save();
      setImmediate(() => sendWelcomeEmail(email, name));
    }

    const token = generateToken({ email });
    res.json({ status: "success", message: "Google auth successful", token });
  } catch (err) {
    console.error("Google Auth error:", err);
    res.json({ status: "failed", message: "Google auth failed" });
  }
}

// ✅ /auth/verify route
async function verifyAuth(req, res) {
	const token = req.headers.authorization;
  
	try {
	  const decoded = await verifyToken(token);
	  const user = await User.findOne({ email: decoded.email });
  
	  if (!user) return res.json({ status: "failed", message: "User not found!" });
	  if (user.blocked) return res.json({ status: "failed", message: "User is blocked" });
  
	  res.json({
		status: "success",
		message: "Verified",
		user: {
		  name: user.name,
		  email: user.email,
		  instagramId: user.instagramId || "",
		  youtubeId: user.youtubeId || "",
		  tiktokId: user.tiktokId || "",
		},
	  });
	} catch {
	  res.json({ status: "failed", message: "Invalid or expired token" });
	}
  }
  

// ✅ Verify email from link
async function verifyEmailToken(req, res) {
  const { token } = req.body;

  try {
    const decoded = await verifyToken(token);
    const user = await User.findOne({ email: decoded.email });

    if (!user) return res.json({ status: "failed", message: "User not found" });
    if (user.isVerified) return res.json({ status: "success", message: "Email already verified" });

    user.isVerified = true;
    await user.save();

    res.json({ status: "success", message: "Email verified successfully" });
  } catch (error) {
    res.json({ status: "failed", message: "Invalid or expired token" });
  }
}

module.exports = {
  VerifyToken,
  signin,
  signup,
  verifyAuth,
  googleAuth,
  verifyEmailToken, // ✅ new
};
