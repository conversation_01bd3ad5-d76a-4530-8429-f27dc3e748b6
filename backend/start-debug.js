// Debug startup script for backend
const dotenv = require('dotenv');
const mongoose = require('mongoose');

// Load environment variables
dotenv.config();

console.log('🔍 Backend Debug Startup');
console.log('========================');

// Check environment variables
console.log('\n📋 Environment Variables:');
console.log('PORT:', process.env.PORT);
console.log('MONGO_URL:', process.env.MONGO_URL ? 'Set' : 'Missing');
console.log('SECRET_KEY:', process.env.SECRET_KEY ? 'Set' : 'Missing');
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('SMTP_HOST:', process.env.SMTP_HOST);
console.log('SMTP_USER:', process.env.SMTP_USER);

// Test MongoDB connection
console.log('\n🔌 Testing MongoDB Connection...');
mongoose.connect(process.env.MONGO_URL)
  .then(() => {
    console.log('✅ MongoDB connected successfully');
    
    // Start the main server
    console.log('\n🚀 Starting main server...');
    require('./server.js');
  })
  .catch((err) => {
    console.error('❌ MongoDB connection failed:', err.message);
    console.log('\n🔧 MongoDB Troubleshooting:');
    console.log('1. Check if MONGO_URL is correct in .env file');
    console.log('2. Verify network connectivity');
    console.log('3. Check MongoDB Atlas whitelist (if using Atlas)');
    process.exit(1);
  });
