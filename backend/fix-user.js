// Quick fix script to allow Google user to login with password
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const dotenv = require('dotenv');

dotenv.config();

const { User } = require('./database');

async function fixUser() {
  try {
    console.log('🔍 Connecting to database...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    const email = '<EMAIL>';
    const newPassword = '123456';

    console.log(`🔍 Looking for user: ${email}`);
    const user = await User.findOne({ email });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('📋 Current user details:');
    console.log('Name:', user.name);
    console.log('Email:', user.email);
    console.log('isGoogleUser:', user.isGoogleUser);
    console.log('isVerified:', user.isVerified);
    console.log('Has password:', !!user.password);

    // Hash the new password
    console.log('🔐 Setting new password...');
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update user
    user.password = hashedPassword;
    user.isVerified = true; // Ensure user is verified
    await user.save();

    console.log('✅ User updated successfully!');
    console.log(`🎯 User ${email} can now login with password: ${newPassword}`);
    console.log('📝 Note: User can still login with Google as well');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

fixUser();
