// const dotenv = require("dotenv")
// dotenv.config();

// function customHash(password) {
//     const salt = process.env.SALT;
//     let transformed = "";
  
//     for (let i = 0; i < password.length; i++) {
//       const charCode = password.charCodeAt(i);
//       const saltCharCode = salt.charCodeAt(i % salt.length);
//       transformed += String.fromCharCode((charCode + saltCharCode) % 256);
//     }
  
//     transformed = transformed.split("").reverse().join("");
  
//     const key = 42;
//     let hashed = "";
//     for (let i = 0; i < transformed.length; i++) {
//       hashed += String.fromCharCode(transformed.charCodeAt(i) ^ key);
//     }
  
//     let hashedHex = "";
//     for (let i = 0; i < hashed.length; i++) {
//       hashedHex += hashed.charCodeAt(i).toString(16).padStart(2, "0");
//     }
  
//     return hashedHex;
// }

// module.exports = {
//     customHash
// }