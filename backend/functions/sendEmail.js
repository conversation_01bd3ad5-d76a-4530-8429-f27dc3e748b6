const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const { User } = require("../database");
const { FeedbackAsk_Email } = require("../emails/feedbackAsk");
const { CampaignSend } = require("../emails/broadCast");
const { default: axios } = require("axios");
dotenv.config();


const SMTP_USER = process.env.SMTP_USER;
const SMTP_PASS = process.env.SMTP_PASS;
const SMTP_HOST = process.env.SMTP_HOST;
const SMTP_PORT = process.env.SMTP_PORT;
const SMTP_SEND_EMAIL = process.env.SMTP_SEND_EMAIL
const SMTP_API_KEY=process.env.SMTP_API_KEY


const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: SMTP_PORT,
  // secure: false,
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

const otpTransporter = nodemailer.createTransport({
  host: "smtp.hostinger.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "#RAINbow-ravi1",
  },
});

const sendWelcomeEmail = async (userEmail, name) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thank you for joining!",
      html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Thanks for Joining</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #000000; color: #ffffff; font-family: Arial, sans-serif;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
          <tr>
            <td align="center" valign="middle">
              <h1 style="color: #ffffff; font-size: 28px; text-align: center;">Hi ${name}, thanks for joining!</h1>
            </td>
          </tr>
        </table>
      </body>
      </html>
      `,
    };

    try {
      await addToBrevoContacts(userEmail, name);
    } catch (err) {
      console.error("⚠️ Brevo contact error (non-blocking):", err.message || err);
    }

    await transporter.sendMail(mailOptions); // ✅ only one arg
    console.log("✅ Welcome email sent");
  } catch (error) {
    console.error("❌ Error sending welcome email:", error.message || error);
  }
};


const sendCampaignToAllUsers = async (campaign) => {
  try {
    const cursor = User.find({}, 'email').cursor();

    for await (const user of cursor) {
      const mailOptions = {
        from: SMTP_SEND_EMAIL,
        to: user.email,
        subject: `New Opportunity Just for You!`,
        html: CampaignSend(campaign),
      };

      try {
        await transporter.sendMail(mailOptions);
        console.log(`✅ Email sent to ${user.email}`);
      } catch (err) {
        console.error(`❌ Failed to send to ${user.email}:`, err.message || err);
      }
    }
  } catch (err) {
    console.error("❌ Error in sendCampaignToAllUsers:", err.message || err);
  }
};

const sendThanksApplyig = async (userEmail, campaign, feedbackLink) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thanks for applying",
      html: FeedbackAsk_Email(campaign, feedbackLink),
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Thanks email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending thanks email to ${userEmail}:`, error.message || error);
  }
};


const addToBrevoContacts = async (email, name) => {
  try {
    const response = await axios.post(
      'https://api.brevo.com/v3/contacts',
      {
        email,
        attributes: {
          NAME: name || "", // Ensure name is always defined
        },
        updateEnabled: true,
      },
      {
        headers: {
          'api-key': SMTP_API_KEY,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log(`✅ Contact added to Brevo: ${email} (ID: ${response.data.id})`);
  } catch (error) {
    const message = error.response?.data?.message || error.message;
    console.error(`❌ Error adding ${email} to Brevo:`, message);
  }
};



// ✅ NEW: Send Verification Email with Link
const sendVerificationLink = async (userEmail, token) => {
  try {
    const verificationUrl = `https://matchably.kr/verify-email?token=${token}`;
    
    const mailOptions = {
      from: "<EMAIL>",
      to: userEmail,
      subject: "Verify your email address - Matchably",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Verify Your Email</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #4f46e5;">Matchably</h1>
                <h2 style="color: #333;">Email Verification</h2>
                <p style="color: #555; font-size: 16px;">Hi there,</p>
                <p style="color: #555; font-size: 16px;">Please click the button below to verify your email address and complete your registration:</p>
                <a href="${verificationUrl}" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  Verify My Email
                </a>
                <p style="margin-top: 30px; color: #777;">If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #4f46e5;">
                  <a href="${verificationUrl}" style="color: #4f46e5;">${verificationUrl}</a>
                </p>
                <p style="color: #999; margin-top: 30px;">This link will expire soon. If you didn’t request this, you can ignore this email.</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await otpTransporter.sendMail(mailOptions);
    console.log(`✅ Verification email sent to ${userEmail}`);
  } catch (error) {
    const msg = error.response?.data || error.message || error;
    console.error(`❌ Failed to send verification email to ${userEmail}:`, msg);
  }
};







module.exports = { sendWelcomeEmail, sendCampaignToAllUsers,sendThanksApplyig, sendVerificationLink, };
