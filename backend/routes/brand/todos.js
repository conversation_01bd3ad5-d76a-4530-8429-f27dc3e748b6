const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");

const brandTodoRouter = express.Router();

// GET /api/brand/todos - Get actionable tasks for the brand dashboard
brandTodoRouter.get("/", VerifyToken, async (req, res) => {
  try {
    // Mock data for brand todos
    // In a real implementation, you would calculate these based on actual data
    const mockTodos = [
      {
        id: 1,
        type: 'pending_approvals',
        title: 'Approve 2 pending contents',
        description: 'Review and approve creator submissions',
        priority: 'high',
        count: 2,
        action: 'Go',
        link: '/brand/campaigns?filter=pending-approval'
      },
      {
        id: 2,
        type: 'messages',
        title: 'Reply to 3 creator messages',
        description: 'Respond to creator inquiries',
        priority: 'medium',
        count: 3,
        action: 'Open Inbox',
        link: '/brand/messages'
      },
      {
        id: 3,
        type: 'missing_assets',
        title: 'Upload product image for "Glow Serum"',
        description: 'Campaign missing product images',
        priority: 'medium',
        count: 1,
        action: 'Upload Now',
        link: '/brand/campaigns/1/assets'
      },
      {
        id: 4,
        type: 'campaign_deadline',
        title: 'Set deadline for "Summer Collection"',
        description: 'Draft campaign needs completion',
        priority: 'low',
        count: 1,
        action: 'Complete',
        link: '/brand/campaigns/3/edit'
      }
    ];

    // Filter to show only top 4 priority items
    const priorityOrder = { 'high': 1, 'medium': 2, 'low': 3 };
    const sortedTodos = mockTodos
      .sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority])
      .slice(0, 4);

    res.json({
      status: "success",
      todos: sortedTodos
    });
  } catch (error) {
    console.error("Error fetching brand todos:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch todos"
    });
  }
});

module.exports = brandTodoRouter;
