const express = require("express");
const brandCampaignRouter = require("./campaigns");
const brandTodoRouter = require("./todos");
const brandCreatorRouter = require("./creators");
const brandPerformanceRouter = require("./performance");
const brandTaskRouter = require("./tasks");

const brandRouter = express.Router();

// Mount sub-routes
brandRouter.use("/campaigns", brandCampaignRouter);
brandRouter.use("/todos", brandTodoRouter);
brandRouter.use("/creators", brandCreatorRouter);
brandRouter.use("/performance", brandPerformanceRouter);
brandRouter.use("/tasks", brandTaskRouter);

module.exports = brandRouter;
