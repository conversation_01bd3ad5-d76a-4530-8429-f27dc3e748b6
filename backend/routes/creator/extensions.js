const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");

const creatorExtensionRouter = express.Router();

// Mock data for creator applications and extensions
const mockCreatorApplications = [
  {
    id: 1,
    campaignId: 1,
    campaignTitle: 'Glow Serum Ampoule',
    creatorId: 'creator1',
    applicationDate: '2025-05-27',
    status: 'Pending',
    participationStatus: 'Applied',
    trackingNumber: null,
    contentSubmitted: false,
    extensionRequests: []
  },
  {
    id: 2,
    campaignId: 2,
    campaignTitle: 'Numbuzin Vitamin Sheet Mask',
    creatorId: 'creator1',
    applicationDate: '2025-05-12',
    status: 'Approved',
    participationStatus: 'Approved',
    trackingNumber: 'TW123456789KR',
    contentSubmitted: false,
    extensionRequests: []
  },
  {
    id: 3,
    campaignId: 3,
    campaignTitle: 'Vita Bright Toner Pack',
    creatorId: 'creator1',
    applicationDate: '2025-05-10',
    status: 'Approved',
    participationStatus: 'Approved',
    trackingNumber: 'TW987654321KR',
    contentSubmitted: false,
    extensionRequests: [
      {
        id: 'ext_001',
        requestedAt: '2025-06-15',
        days: 2,
        reason: 'Need more time for product testing and content creation',
        status: 'Pending'
      }
    ]
  },
  {
    id: 4,
    campaignId: 4,
    campaignTitle: 'Cica Repair Cream',
    creatorId: 'creator1',
    applicationDate: '2025-05-05',
    status: 'Approved',
    participationStatus: 'Approved',
    trackingNumber: 'TW555666777KR',
    contentSubmitted: false,
    extensionRequests: [
      {
        id: 'ext_002',
        requestedAt: '2025-06-10',
        days: 3,
        reason: 'Product arrived late due to shipping delays',
        status: 'Approved'
      }
    ]
  },
  {
    id: 5,
    campaignId: 5,
    campaignTitle: 'Calming Sheet Mask',
    creatorId: 'creator1',
    applicationDate: '2025-05-01',
    status: 'Approved',
    participationStatus: 'Approved',
    trackingNumber: 'TW111222333KR',
    contentSubmitted: true,
    extensionRequests: [
      {
        id: 'ext_003',
        requestedAt: '2025-06-01',
        days: 1,
        reason: 'Technical issues with video recording',
        status: 'Rejected'
      }
    ]
  }
];

// GET /api/creator/applications - Get creator's campaign applications
creatorExtensionRouter.get("/applications", async (req, res) => {
  try {
    // In real implementation, filter by authenticated creator ID
    const applications = mockCreatorApplications.map(app => ({
      ...app,
      extensionStatus: getExtensionStatus(app),
      canRequestExtension: canRequestExtension(app)
    }));

    res.json({
      status: "success",
      applications: applications
    });
  } catch (error) {
    console.error("Error fetching creator applications:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch applications"
    });
  }
});

// POST /api/creator/extensions/request - Request extension for a campaign
creatorExtensionRouter.post("/request", async (req, res) => {
  try {
    const { campaignId, days, reason } = req.body;

    // Validate input
    if (!campaignId || !days || !reason) {
      return res.json({
        status: "failed",
        message: "Campaign ID, days, and reason are required"
      });
    }

    if (days < 1 || days > 5) {
      return res.json({
        status: "failed",
        message: "Extension days must be between 1 and 5"
      });
    }

    if (reason.length < 10) {
      return res.json({
        status: "failed",
        message: "Reason must be at least 10 characters"
      });
    }

    // Find the application
    const appIndex = mockCreatorApplications.findIndex(
      app => app.campaignId === parseInt(campaignId)
    );

    if (appIndex === -1) {
      return res.json({
        status: "failed",
        message: "Application not found"
      });
    }

    const application = mockCreatorApplications[appIndex];

    // Check if extension can be requested
    if (!canRequestExtension(application)) {
      return res.json({
        status: "failed",
        message: "Extension cannot be requested for this campaign"
      });
    }

    // Check monthly limit (simplified - in real app, check database)
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const thisMonthRequests = mockCreatorApplications
      .flatMap(app => app.extensionRequests)
      .filter(req => {
        const reqDate = new Date(req.requestedAt);
        return reqDate.getMonth() === currentMonth && reqDate.getFullYear() === currentYear;
      });

    if (thisMonthRequests.length >= 1) {
      return res.json({
        status: "failed",
        message: "You can only request 1 extension per calendar month"
      });
    }

    // Create extension request
    const extensionRequest = {
      id: `ext_${Date.now()}`,
      requestedAt: new Date().toISOString().split('T')[0],
      days: parseInt(days),
      reason: reason,
      status: 'Pending'
    };

    // Add to application
    mockCreatorApplications[appIndex].extensionRequests.push(extensionRequest);

    // In real implementation:
    // 1. Save to database
    // 2. Send notification to brand
    // 3. Send confirmation email to creator

    res.json({
      status: "success",
      message: "Extension request submitted successfully",
      extensionRequest: extensionRequest
    });
  } catch (error) {
    console.error("Error requesting extension:", error);
    res.json({
      status: "failed",
      message: "Failed to submit extension request"
    });
  }
});

// Helper functions
function getExtensionStatus(application) {
  if (application.extensionRequests.length === 0) {
    return canRequestExtension(application) ? 'available' : 'not_available';
  }

  const latestRequest = application.extensionRequests[application.extensionRequests.length - 1];
  
  switch (latestRequest.status) {
    case 'Pending':
      return `⏳ +${latestRequest.days}d Pending`;
    case 'Approved':
      return `✅ +${latestRequest.days}d Approved`;
    case 'Rejected':
      return `❌ Rejected`;
    default:
      return 'unknown';
  }
}

function canRequestExtension(application) {
  // Check all conditions for extension eligibility
  const conditions = {
    isApproved: application.status === 'Approved',
    hasTracking: !!application.trackingNumber,
    notSubmitted: !application.contentSubmitted,
    noPendingRequest: !application.extensionRequests.some(req => req.status === 'Pending')
  };

  return Object.values(conditions).every(condition => condition);
}

function getNotAvailableReason(application) {
  if (application.status !== 'Approved') {
    return 'Application not approved';
  }
  if (!application.trackingNumber) {
    return 'Tracking number not entered';
  }
  if (application.contentSubmitted) {
    return 'Content already submitted';
  }
  if (application.extensionRequests.some(req => req.status === 'Pending')) {
    return 'Extension already requested this month';
  }
  return 'Extension not available';
}

module.exports = creatorExtensionRouter;
