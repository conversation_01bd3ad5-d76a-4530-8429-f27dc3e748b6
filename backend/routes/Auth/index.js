const express = require("express");
const {
  signin,
  signup,
  verifyAuth,
  googleAuth,
  verifyEmailToken,
  forgotPassword,
  setPasswordForGoogleUser,
} = require("../../middlewares/auth");

const rateLimit = require("express-rate-limit"); // ✅ Step 1: Import rate limiter

const Auth = express.Router();

// ✅ Step 2: Configure signup rate limiter
const signupLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 10, // Max 5 signups per 10 min per IP
  message: {
    status: "failed",
    message: "Too many signup attempts. Try again later.",
  },
});

const signinLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 20, // ✅ allow up to 20 login attempts
  message: {
    status: "failed",
    message: "Too many login attempts. Please try again after 10 minutes.",
  },
});


// ✅ Step 3: Apply routes with limiter
Auth.post("/signup", signupLimiter, signup); // 🔐 rate-limited
Auth.post("/signin", signinLimiter, signin);
Auth.get("/verify", verifyAuth);
Auth.post("/google-auth", googleAuth);
Auth.post("/verify-email", verifyEmailToken);
Auth.post("/forgot-password", forgotPassword);
Auth.post("/set-password", setPasswordForGoogleUser); // ✅ new

module.exports = {
  Auth,
};
