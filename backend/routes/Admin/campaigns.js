const express = require("express");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const { addCampaign, getCampaigns, editCmpaigns, deleteCampaigns, getBrands } = require("../../middlewares/campaign");
const Campaigns = express.Router();

Campaigns.post("/addCampaign", VerifyTokenAuth, addCampaign);
Campaigns.get("/", getCampaigns);
Campaigns.post("/editCampaign/:id", VerifyTokenAuth, editCmpaigns);
Campaigns.delete("/:id", VerifyTokenAuth, deleteCampaigns);
Campaigns.get("/brands", VerifyTokenAuth, getBrands);

module.exports = {
    Campaigns
}