function FeedbackAsk_Email(campaign, feedbackLink){
    return `
    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Thank You for Applying</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <style>
    body {
      background-color: #121212;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 0;
      color: #e0e0e0;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      padding: 30px;
      background-color: #1f1f1f;
      border-radius: 12px;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    }
    h1 {
      color: #f9a825;
      font-size: 22px;
      text-align: center;
      margin-bottom: 24px;
      font-weight: 600;
    }
    p {
      font-size: 16px;
      line-height: 1.6;
      color: #d1d1d1;
    }
    a.button {
      display: inline-block;
      margin: 20px 0;
      padding: 14px 24px;
      background-color: #f9a825;
      color: #1f1f1f;
      text-decoration: none;
      border-radius: 6px;
      font-weight: bold;
      text-align: center;
      transition: background-color 0.3s ease;
    }
    a.button:hover {
      background-color: #f57f17;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      font-size: 13px;
      color: #aaa;
      border-top: 1px solid #333;
      padding-top: 20px;
    }
    a {
      color: #f9a825;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <div class="container">
    <h1>Thank You for Applying to the Campaign!</h1>
    <p>
      We appreciate your interest in the 
      <a href="https://matchably.kr/campaign/${campaign._id}">
        "${campaign.campaignTitle}"
      </a> campaign. Your application has been successfully submitted.
    </p>
    <footer>
      &copy; ${new Date().getFullYear()} Guideway Consulting. All rights reserved.
    </footer>
  </div>

</body>
</html>

    `
}

module.exports = {
    FeedbackAsk_Email
}