function CampaignSend(campaign){
    return `
        <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Campaign Details</title>
</head>
<body>
  <div style="background-color: #000000; color: #ffffff; padding: 40px 20px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.6;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #1a1a1a; border-radius: 12px; padding: 30px; box-shadow: 0 4px 10px rgba(0,0,0,0.5);">
      
      <h1 style="color: #f9d342; font-size: 28px; margin-bottom: 10px;">${campaign.campaignTitle}</h1>
      
      <p style="font-size: 16px; margin: 10px 0;">
        <strong style="color: #ccc;">Brand:</strong> ${campaign.brandName}
      </p>
      
      <p style="font-size: 15px; color: #ddd; margin: 20px 0;">
        ${campaign.productDescription}
      </p>
  
      <p style="margin: 20px 0;">
        <strong style="color: #ccc;">Deadline:</strong> ${new Date(campaign.deadline).toLocaleDateString()}
      </p>
  
      <a href="https://matchably.kr/campaign/${campaign._id}"
         style="display: inline-block; background-color: #f9d342; color: #000000; padding: 12px 24px; font-weight: bold; text-decoration: none; border-radius: 6px; transition: background-color 0.3s;">
        View Campaign
      </a>
      
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #333;">
      
      <p style="font-size: 14px; color: #888; text-align: center;">
        &copy; ${new Date().getFullYear()} Guideway Consulting. All rights reserved.
      </p>
    </div>
  </div>
</body>
</html>
    `
}

module.exports = {
    CampaignSend
}