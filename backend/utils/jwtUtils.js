// utils/jwtUtils.js
const jwt = require("jsonwebtoken");
const secret = process.env.SECRET_KEY;

function generateToken(payload, expiresIn = "24h") {
	return jwt.sign(payload, secret, { expiresIn });
}

function verifyToken(token) {
	return new Promise((resolve, reject) => {
		jwt.verify(token, secret, (err, decoded) => {
			if (err) return reject(err);
			resolve(decoded);
		});
	});
}

module.exports = {
	generateToken,
	verifyToken,
};
